import { mazius } from "@/app/font";
import styles from "./Hero.module.css";

const Hero = () => {
  return (
    <div className="relative container mt-[5vh] flex w-full flex-col justify-center px-4 md:mt-[10vh] md:px-6">
      <div className="title-row flex flex-col gap-4 md:flex-row md:justify-between md:gap-0">
        <h2 className="text-[clamp(5rem,8vw,10rem)] leading-tight font-bold text-zinc-800 md:text-[clamp(5rem,8vw,15rem)]">
          Creative{" "}
        </h2>
        <h5 className="hidden self-start text-base text-zinc-500 md:inline-block md:self-center md:text-xl">
          Currently Available
          <br />
          FOR Freelance Worldwide
        </h5>
      </div>

      <div className="title-row mt-4 flex flex-col items-start gap-6 md:mt-0 md:flex-row md:items-end md:justify-between md:gap-0">
        <h5 className="order-2 text-sm text-zinc-500 md:order-1 md:text-lg">
          Empowering Ideas Through Code
          <br />
          Anytime, Anywhere
        </h5>

        <div className="order-1 flex flex-wrap items-center gap-2 md:order-2 md:gap-4">
          <span className={`${mazius.className} inline-block text-[#14CF93]`}>
            <svg
              id="animatedText"
              className={`${styles.animatedText} h-[70px] w-[180px] md:h-[100px] md:w-[200px] lg:h-[120px] lg:w-[330px]`}
              viewBox="0 0 330 127"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* SVG paths remain unchanged */}
              <g filter="url(#filter0_d_8_11)">
                <path
                  d="M323.392 1.2475L325.12 3.40765L325.285 3.61371L325.208 3.86664L293.27 107.946L307.105 101.902L307.607 101.683L308.068 103.066L308.205 103.477L307.817 103.671L287.945 113.607L287.767 113.696L287.578 113.639C284.508 112.718 282.256 109.968 279.394 105.962L279.251 105.76L279.324 105.524L309.266 9.13617L294.946 14.2709L294.482 14.437L294.309 13.976L293.878 12.8237L293.704 12.3598L294.165 12.1821L322.821 1.0932L323.163 0.961365L323.392 1.2475Z"
                  stroke="#14CF93"
                />
                <path
                  d="M257.251 40.6603C260.095 40.2969 264.016 41.0425 267.789 42.1564C271.597 43.2806 275.332 44.8038 277.807 46.0411L278.182 46.2286L278.061 46.632L260.509 105.093L274.079 99.4503L274.771 99.1622V101.39L274.474 101.522L247.113 113.617L246.836 113.74L246.599 113.551C246.228 113.255 245.706 112.807 245.236 112.318C244.782 111.846 244.315 111.273 244.133 110.726L244.082 110.574L244.129 110.421L260.231 58.1232C256.968 64.5777 250.979 74.5284 244.849 84.2306C237.253 96.2556 229.396 107.97 226.143 112.308L225.959 112.554L225.656 112.5C221.375 111.749 217.957 108.865 215.621 105.26C213.285 101.655 211.995 97.2771 211.995 93.4318C211.995 87.4463 214.187 81.1948 217.599 75.1964C221.015 69.1939 225.67 63.4129 230.642 58.3595C235.615 53.3057 240.916 48.9672 245.632 45.8527C247.989 44.2956 250.208 43.0399 252.172 42.1534C254.127 41.2709 255.865 40.7374 257.251 40.6603ZM261.076 51.1447C257.314 48.981 252.827 47.3097 247.974 47.8488L247.947 47.8517H247.919C247.236 47.8518 246.369 48.2581 245.337 49.1368C244.319 50.0033 243.21 51.271 242.055 52.8556C239.745 56.0223 237.294 60.3878 235.052 65.1505C232.81 69.9108 230.785 75.0514 229.321 79.7589C227.854 84.4777 226.963 88.7215 226.963 91.7042C226.963 94.1244 227.249 96.1351 227.859 97.5753C228.426 98.9134 229.246 99.7104 230.352 99.9532C230.414 99.8885 230.494 99.8007 230.591 99.6866C230.825 99.41 231.14 99.0087 231.53 98.4943C232.309 97.4665 233.367 96.0063 234.62 94.2286C237.125 90.6742 240.403 85.8636 243.775 80.7374C247.148 75.611 250.613 70.1719 253.495 65.3605C256.383 60.5391 258.667 56.3768 259.694 53.7911L259.704 53.7667L259.716 53.7443L261.076 51.1447Z"
                  stroke="#14CF93"
                />
                <path
                  d="M178.052 48.6482C178.052 49.7554 177.778 51.3392 177.321 53.24C176.86 55.1513 176.204 57.4203 175.419 59.9148C173.85 64.9048 171.759 70.8228 169.671 76.6277C167.582 82.4371 165.496 88.1317 163.932 92.6921C163.15 94.9723 162.5 96.9616 162.047 98.5349C161.587 100.133 161.349 101.237 161.349 101.784C161.349 102.929 161.617 103.734 162.125 104.317C162.608 104.871 163.36 105.283 164.474 105.559C164.523 105.508 164.585 105.443 164.657 105.358C164.877 105.103 165.179 104.722 165.557 104.222C166.311 103.224 167.349 101.775 168.605 99.9587C171.116 96.327 174.487 91.243 178.174 85.407C185.549 73.7327 194.173 59.0698 199.703 47.0056L200.997 43.9871L201.117 43.7058L201.422 43.6853L216.111 42.6775L216.839 42.6267L216.622 43.325L197.49 104.827L210.49 99.7341L211.065 99.5095L211.165 100.118L211.31 100.982L211.373 101.362L211.022 101.52L184.093 113.616L183.815 113.741L183.576 113.551C183.206 113.254 182.684 112.807 182.214 112.318C181.76 111.846 181.293 111.273 181.111 110.726L181.059 110.571L181.109 110.416L199.804 52.073C194.384 63.8184 187.14 77.8382 180.031 89.5164C175.994 96.1494 171.991 102.042 168.386 106.336C166.584 108.482 164.87 110.242 163.29 111.497C161.722 112.743 160.23 113.537 158.877 113.658L158.753 113.669L158.639 113.621C155.062 112.119 152.272 110.9 150.396 109.192C148.471 107.44 147.532 105.206 147.532 101.784C147.532 100.643 147.808 98.9737 148.27 96.949C148.734 94.9135 149.396 92.4824 150.19 89.8025C151.777 84.4418 153.895 78.0618 156.019 71.8152C158.145 65.563 160.275 59.451 161.895 54.6052C162.704 52.1833 163.385 50.0847 163.87 48.45C164.162 47.4687 164.375 46.6745 164.509 46.0847L151.714 52.5505L151.319 52.7498L151.073 52.3816L150.496 51.5173L150.191 51.0593L150.676 50.7996L168.964 41.0076L169.17 40.8972L169.386 40.9841C173.103 42.4711 175.769 44.9989 177.971 48.3748L178.052 48.4998V48.6482Z"
                  stroke="#14CF93"
                />
                <path
                  d="M140.144 38.212C142.89 38.212 145.816 38.3557 148.284 38.9268C150.73 39.4926 152.865 40.5093 153.835 42.3682L153.931 42.5518L153.869 42.7491L150.99 51.9649L150.879 52.3155H149.326L149.188 52.2032C146.817 50.2631 144.098 48.1535 141.567 46.5313C140.301 45.7201 139.091 45.0372 138.003 44.5587C136.907 44.0764 135.974 43.8205 135.248 43.8204C128.636 43.8204 124.744 47.2017 121.205 51.1426C121.253 54.4357 122.542 57.6073 124.489 60.6309C126.473 63.7131 129.121 66.6082 131.77 69.2842C134.4 71.9416 137.064 74.4122 138.998 76.5792C139.969 77.6672 140.782 78.7061 141.329 79.6837C141.875 80.6564 142.188 81.622 142.08 82.546V82.5518C140.031 98.5069 128.02 108.861 112.321 112.495L112.32 112.494C110.993 112.806 109.326 112.702 107.6 112.312C105.864 111.919 104.026 111.226 102.342 110.324C100.66 109.423 99.1104 108.302 97.9661 107.038C96.8249 105.777 96.0523 104.335 96.012 102.805L96.0062 102.569L96.1839 102.414L103.817 95.7901L104.248 95.4161L104.562 95.8936C106.161 98.327 108.61 100.329 111.279 101.796C113.937 103.257 116.78 104.168 119.152 104.451C124.877 104.442 130.686 101.941 133.614 97.9922C133.898 95.3192 132.745 92.8287 130.762 90.3077C128.742 87.7395 125.932 85.2273 123.078 82.5274C117.436 77.1894 111.607 71.1194 112.576 62.8458L112.576 62.8389C113.318 57.2062 117.295 51.058 122.492 46.334C127.688 41.6104 134.211 38.212 140.144 38.212Z"
                  stroke="#14CF93"
                />
                <path
                  d="M102.123 42.1083L103.707 42.3964L104.257 42.496L104.096 43.0321L84.5584 108.157L98.3524 101.193L98.8006 100.968L99.0242 101.417L99.4568 102.28L99.6688 102.704L99.2567 102.938L80.5369 113.595L80.2518 113.757L79.9891 113.56C76.4785 110.927 74.708 109.006 72.0848 104.634L71.9705 104.443L72.035 104.231L88.7586 48.8153L77.5526 52.1512L77.0887 52.2889L76.9354 51.83L76.6473 50.9657L76.4881 50.4901L76.9647 50.3329L101.877 42.1249L101.998 42.0848L102.123 42.1083ZM115.991 9.49597L116.423 9.78406L116.764 10.0116L116.607 10.3915C114.866 14.5993 111.884 21.4248 111.46 27.0858L111.415 27.6864L110.833 27.5311C109.77 27.2476 108.382 26.9624 106.9 26.6552C105.426 26.3496 103.865 26.023 102.482 25.6591L102.263 25.6024L102.162 25.3993C101.229 23.5335 101.172 20.9605 101.577 18.6805C101.781 17.5307 102.107 16.4299 102.516 15.4979C102.922 14.5751 103.427 13.7778 104.013 13.2762L104.087 13.2118L104.181 13.1815L115.557 9.43738L115.788 9.36121L115.991 9.49597Z"
                  stroke="#14CF93"
                />
                <path
                  d="M70.6504 5.24969C75.7957 5.5416 83.1885 10.3721 83.1885 20.4245C83.1884 24.2347 82.3095 28.607 81.0137 33.9343C77.9585 48.8893 67.0583 66.133 53.708 81.5192C40.3435 96.9218 24.4628 110.534 11.373 118.2L11.208 118.296L11.0215 118.258C10.5892 118.172 10.194 117.988 9.90527 117.674C9.61085 117.352 9.46784 116.938 9.46777 116.472V116.185L9.71582 116.04C11.2108 115.168 12.7076 114.183 14.3086 113.086C10.3417 110.949 7.36598 106.517 5.32031 100.653L5.26465 100.496L5.31543 100.337L31.1885 18.737L14.6348 26.0651L14.1543 26.278L13.9648 25.7878L13.2451 23.9157L13.0762 23.4763L13.5039 23.2809L43.7285 9.46454L45.4561 8.60126L45.7158 8.47137L45.957 8.63153C46.6792 9.113 47.2521 9.76436 47.5996 10.6022C47.9459 11.4374 48.0564 12.4293 47.9033 13.5778L47.9023 13.5886L47.9004 13.5983C45.517 27.1762 40.2132 43.9144 34.6709 60.5954C29.1316 77.2673 23.3541 93.8816 19.9736 107.31C19.9935 107.827 20.2951 108.282 20.7119 108.541C33.3446 98.9366 46.5992 85.7676 57.0498 71.572C67.5259 57.3416 75.1449 42.1329 76.5527 28.489C74.4324 25.6934 71.6401 22.8496 68.6533 20.8005C65.5982 18.7045 62.4154 17.4893 59.5742 17.8952L59.0039 17.9763V17.4001C59.0039 17.1959 59.0895 16.9901 59.165 16.8356C59.2503 16.6614 59.3692 16.4633 59.5098 16.2507C59.7919 15.824 60.1885 15.2985 60.6582 14.7165C61.5996 13.55 62.8593 12.1236 64.1416 10.7419C65.4243 9.35987 66.7364 8.01546 67.7852 7.01434C68.3085 6.51478 68.7726 6.09457 69.1377 5.79657C69.3192 5.64842 69.4863 5.52259 69.6289 5.43134C69.6997 5.38607 69.7742 5.34265 69.8486 5.30927C69.9121 5.28085 70.0262 5.23602 70.1602 5.23602L70.6504 5.24969Z"
                  stroke="#14CF93"
                />
                {/* Other paths remain the same */}
              </g>
              <defs>
                <filter
                  id="filter0_d_8_11"
                  x="0.738159"
                  y="0.362793"
                  width="329.102"
                  height="126.46"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset dy="4" />
                  <feGaussianBlur stdDeviation="2" />
                  <feComposite in2="hardAlpha" operator="out" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_8_11"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_8_11"
                    result="shape"
                  />
                </filter>
              </defs>
            </svg>
          </span>
          <span className="flex-end text-end! text-[clamp(5rem,8vw,10rem)] leading-tight font-bold text-zinc-800 md:text-[clamp(5rem,8vw,15rem)]">
            Designer
          </span>
        </div>
      </div>
    </div>
  );
};

export default Hero;
